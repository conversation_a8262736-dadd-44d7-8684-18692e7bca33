<?php

namespace Deployer;

require 'recipe/common.php';
require 'contrib/rsync.php';

// Disable git repository usage
set('repository', false);

set('rsync_src', __DIR__ . '/');
set('rsync_dest', '{{release_path}}');
set('rsync', [
  'exclude' => [
    // Version control and deployment files
    '.git',
    '.gitignore',
    'deploy.php',

    // Development and build files
    '.idea',
    '.DS_Store',
    'site/',

    // Local overrides and generated files
    'compose.override.yml',
    'env/.common.env',
    'env/.apache.env',
    'env/.php.env',

    // Application files that will be generated during build
    'html/vendor/',
    'html/web/core/',
    'html/web/modules/contrib/',
    'html/web/themes/contrib/',
    'html/web/themes/custom/**/node_modules/',
    'html/web/themes/custom/**/css/',
    'html/.phpunit.cache/',
    'html/web/clover.xml',
    'html/private/',

    // Database and temporary files
    'docker/mysql/*.sql',
    'docker/mysql/*.dump',
  ],
  'exclude-file' => false,
  'include' => [],
  'include-file' => false,
  'filter' => [],
  'filter-file' => false,
  'filter-perdir' => false,
  'flags' => 'rz',
  'options' => ['delete'],
  'timeout' => 300,
]);

task('deploy:build', function() {
  run('cd {{deploy_path}}/current && task build');
});

task('deploy:up', function() {
  run('cd {{deploy_path}}/current && task up');
});

host('aws')
  ->set('deploy_path', '/root/starterkit')
  ->setRemoteUser('root');

task('deploy', [
  'deploy:prepare',
  'deploy:release',
  'rsync',
  'deploy:symlink',
  'deploy:build',
  'deploy:up',
])->desc('Deploy your project');

// Override the default update_code task to use rsync instead of git
task('deploy:update_code', function() {
  invoke('rsync');
});
