<?php

namespace Deployer;

require 'recipe/common.php';
require 'contrib/rsync.php';

// Disable git repository usage
set('repository', false);

set('rsync_src', __DIR__ . '/');
set('rsync_dest', '{{release_path}}');
set('rsync_excludes', [
  'html/web/themes/custom/base_starterkit/node_modules',
  'html/vendor',
  'html/web/core',
]);

task('deploy:build', function() {
  run('cd {{deploy_path}}/current && task build');
});

task('deploy:up', function() {
  run('cd {{deploy_path}}/current && task up');
});

host('aws')
  ->set('deploy_path', '/root/starterkit')
  ->setRemoteUser('root');

task('deploy', [
  'deploy:prepare',
  'deploy:release',
  'rsync',
  'deploy:symlink',
  'deploy:build',
  'deploy:up',
])->desc('Deploy your project');

// Override the default update_code task to use rsync instead of git
task('deploy:update_code', function() {
  invoke('rsync');
});
